export interface GraphNode {
  id: string;
  label: string;
  type: 'concept' | 'verse' | 'person' | 'place' | 'theme';
  description?: string;
  position?: { x: number; y: number };
  color?: string;
  size?: number;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  type: 'relates' | 'references' | 'explains' | 'connects';
  strength?: number;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  nodes?: GraphNode[];
  edges?: GraphEdge[];
  timestamp?: Date;
}
