"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { GraphNode, GraphEdge } from "../types/graph";

interface KnowledgeGraphProps {
    nodes: GraphNode[];
    edges: GraphEdge[];
}

interface LayoutNode extends GraphNode {
    position: { x: number; y: number };
    vx: number;
    vy: number;
}

export default function KnowledgeGraph({ nodes, edges }: KnowledgeGraphProps) {
    const svgRef = useRef<SVGSVGElement>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
    const [layoutedNodes, setLayoutedNodes] = useState<LayoutNode[]>([]);
    const [isDragging, setIsDragging] = useState(false);
    const [draggedNode, setDraggedNode] = useState<string | null>(null);
    const [transform, setTransform] = useState({ x: 0, y: 0, scale: 1 });
    const [isPanning, setIsPanning] = useState(false);
    const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });

    useEffect(() => {
        const updateDimensions = () => {
            if (svgRef.current) {
                const rect = svgRef.current.parentElement?.getBoundingClientRect();
                if (rect) {
                    setDimensions({ width: rect.width, height: rect.height - 20 });
                }
            }
        };

        updateDimensions();
        window.addEventListener('resize', updateDimensions);
        return () => window.removeEventListener('resize', updateDimensions);
    }, []);

    // Initialize layout when nodes change
    useEffect(() => {
        if (nodes.length > 0 && dimensions.width > 0) {
            const initialNodes = layoutNodes(nodes, edges, dimensions.width, dimensions.height);
            setLayoutedNodes(initialNodes);
        }
    }, [nodes, edges, dimensions]);

    // Improved force-directed layout simulation
    const layoutNodes = (nodes: GraphNode[], edges: GraphEdge[], width: number, height: number): LayoutNode[] => {
        if (nodes.length === 0) return [];

        const layoutNodes: LayoutNode[] = nodes.map((node, index) => ({
            ...node,
            position: {
                // Better initial positioning in a circle
                x: width / 2 + Math.cos((index / nodes.length) * 2 * Math.PI) * Math.min(width, height) * 0.2,
                y: height / 2 + Math.sin((index / nodes.length) * 2 * Math.PI) * Math.min(width, height) * 0.2,
            },
            vx: 0,
            vy: 0,
        }));

        // Improved simulation with better spacing
        for (let i = 0; i < 150; i++) {
            // Apply forces
            layoutNodes.forEach((node, nodeIndex) => {
                let fx = 0, fy = 0;

                // Stronger repulsion from other nodes to reduce crowding
                layoutNodes.forEach((other, otherIndex) => {
                    if (nodeIndex !== otherIndex) {
                        const dx = node.position.x - other.position.x;
                        const dy = node.position.y - other.position.y;
                        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                        const minDistance = 100; // Minimum distance between nodes (increased for bigger circles)
                        const force = Math.max(3000 / (distance * distance), minDistance / distance);
                        fx += (dx / distance) * force;
                        fy += (dy / distance) * force;
                    }
                });

                // Weaker attraction from connected nodes
                edges.forEach(edge => {
                    const isSource = edge.source === node.id;
                    const isTarget = edge.target === node.id;

                    if (isSource || isTarget) {
                        const connectedId = isSource ? edge.target : edge.source;
                        const connectedNode = layoutNodes.find(n => n.id === connectedId);

                        if (connectedNode) {
                            const dx = connectedNode.position.x - node.position.x;
                            const dy = connectedNode.position.y - node.position.y;
                            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                            const idealDistance = 150; // Ideal distance between connected nodes (increased)
                            const force = (distance - idealDistance) * 0.02;
                            fx += (dx / distance) * force;
                            fy += (dy / distance) * force;
                        }
                    }
                });

                // Weaker center force
                const centerX = width / 2;
                const centerY = height / 2;
                const centerDx = centerX - node.position.x;
                const centerDy = centerY - node.position.y;
                fx += centerDx * 0.0005;
                fy += centerDy * 0.0005;

                // Update velocity with damping
                node.vx = (node.vx + fx) * 0.85;
                node.vy = (node.vy + fy) * 0.85;

                // Update position
                node.position.x += node.vx;
                node.position.y += node.vy;

                // Keep in bounds with more padding
                const padding = 100; // Increased padding for bigger circles
                node.position.x = Math.max(padding, Math.min(width - padding, node.position.x));
                node.position.y = Math.max(padding, Math.min(height - padding, node.position.y));
            });
        }

        return layoutNodes;
    };

    const getNodeColor = (type: string) => {
        switch (type) {
            case 'concept': return '#3b82f6'; // blue
            case 'verse': return '#10b981'; // green
            case 'person': return '#f59e0b'; // amber
            case 'place': return '#8b5cf6'; // purple
            case 'theme': return '#ef4444'; // red
            default: return '#6b7280'; // gray
        }
    };

    const getNodeSize = (type: string) => {
        switch (type) {
            case 'concept': return 35; // Increased sizes
            case 'verse': return 32;
            case 'person': return 38;
            case 'place': return 34;
            case 'theme': return 40;
            default: return 30;
        }
    };

    // Mouse interaction handlers
    const handleMouseDown = useCallback((event: React.MouseEvent, nodeId?: string) => {
        event.preventDefault();
        const rect = svgRef.current?.getBoundingClientRect();
        if (!rect) return;

        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        if (nodeId) {
            setIsDragging(true);
            setDraggedNode(nodeId);
        } else {
            setIsPanning(true);
            setLastPanPoint({ x, y });
        }
    }, []);

    const handleMouseMove = useCallback((event: React.MouseEvent) => {
        const rect = svgRef.current?.getBoundingClientRect();
        if (!rect) return;

        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        if (isDragging && draggedNode) {
            // Update dragged node position
            setLayoutedNodes(prev => prev.map(node =>
                node.id === draggedNode
                    ? { ...node, position: { x: (x - transform.x) / transform.scale, y: (y - transform.y) / transform.scale } }
                    : node
            ));
        } else if (isPanning) {
            // Pan the view
            const dx = x - lastPanPoint.x;
            const dy = y - lastPanPoint.y;
            setTransform(prev => ({ ...prev, x: prev.x + dx, y: prev.y + dy }));
            setLastPanPoint({ x, y });
        }
    }, [isDragging, draggedNode, isPanning, lastPanPoint, transform]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        setDraggedNode(null);
        setIsPanning(false);
    }, []);

    const handleWheel = useCallback((event: React.WheelEvent) => {
        event.preventDefault();
        const rect = svgRef.current?.getBoundingClientRect();
        if (!rect) return;

        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        const delta = event.deltaY > 0 ? 0.9 : 1.1;
        const newScale = Math.max(0.1, Math.min(3, transform.scale * delta));

        setTransform(prev => ({
            scale: newScale,
            x: x - (x - prev.x) * (newScale / prev.scale),
            y: y - (y - prev.y) * (newScale / prev.scale),
        }));
    }, [transform]);



    if (nodes.length === 0) {
        return (
            <div className="h-full flex items-center justify-center text-slate-500 dark:text-slate-400">
                <div className="text-center">
                    <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <p className="text-sm">
                        Start a conversation to see the knowledge graph
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full relative">
            <svg
                ref={svgRef}
                width="100%"
                height="100%"
                className="rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 cursor-grab"
                onMouseDown={(e) => handleMouseDown(e)}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                onWheel={handleWheel}
                style={{ cursor: isDragging ? 'grabbing' : isPanning ? 'grabbing' : 'grab' }}
            >
                <g transform={`translate(${transform.x}, ${transform.y}) scale(${transform.scale})`}>
                    {/* Edges */}
                    {edges.map(edge => {
                        const sourceNode = layoutedNodes.find(n => n.id === edge.source);
                        const targetNode = layoutedNodes.find(n => n.id === edge.target);

                        if (!sourceNode || !targetNode) return null;

                        return (
                            <g key={edge.id}>
                                <line
                                    x1={sourceNode.position.x}
                                    y1={sourceNode.position.y}
                                    x2={targetNode.position.x}
                                    y2={targetNode.position.y}
                                    stroke="#94a3b8"
                                    strokeWidth="3"
                                    strokeOpacity="0.6"
                                    strokeDasharray="8,4"
                                />
                                {edge.label && transform.scale > 0.5 && (
                                    <text
                                        x={(sourceNode.position.x + targetNode.position.x) / 2}
                                        y={(sourceNode.position.y + targetNode.position.y) / 2}
                                        textAnchor="middle"
                                        className="text-xs fill-gray-600 dark:fill-gray-400 font-medium pointer-events-none"
                                        dy="-8"
                                        style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.8)' }}
                                    >
                                        {edge.label}
                                    </text>
                                )}
                            </g>
                        );
                    })}

                    {/* Nodes */}
                    {layoutedNodes.map(node => {
                        const nodeSize = node.size || getNodeSize(node.type);
                        const nodeColor = node.color || getNodeColor(node.type);

                        return (
                            <g key={node.id}>
                                {/* Node shadow */}
                                <circle
                                    cx={node.position.x + 3}
                                    cy={node.position.y + 3}
                                    r={nodeSize}
                                    fill="rgba(0,0,0,0.15)"
                                />
                                {/* Main node */}
                                <circle
                                    cx={node.position.x}
                                    cy={node.position.y}
                                    r={nodeSize}
                                    fill={nodeColor}
                                    stroke="#fff"
                                    strokeWidth="4"
                                    className="cursor-pointer hover:opacity-90 transition-opacity"
                                    style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}
                                    onMouseDown={(e) => {
                                        e.stopPropagation();
                                        handleMouseDown(e, node.id);
                                    }}
                                />
                                {/* Node label */}
                                {transform.scale > 0.3 && (
                                    <text
                                        x={node.position.x}
                                        y={node.position.y + 4}
                                        textAnchor="middle"
                                        className="text-xs font-semibold fill-white pointer-events-none"
                                        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                                    >
                                        {node.label.length > 10 ? node.label.substring(0, 10) + '...' : node.label}
                                    </text>
                                )}
                                {/* Tooltip */}
                                {node.description && (
                                    <title>{`${node.label}: ${node.description}`}</title>
                                )}
                            </g>
                        );
                    })}
                </g>
            </svg>

            {/* Controls */}
            <div className="absolute top-4 right-4 space-y-3">
                {/* Zoom Controls */}
                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="flex flex-col space-y-2">
                        <button
                            onClick={() => setTransform(prev => ({ ...prev, scale: Math.min(3, prev.scale * 1.2) }))}
                            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="Zoom In"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </button>
                        <button
                            onClick={() => setTransform(prev => ({ ...prev, scale: Math.max(0.1, prev.scale * 0.8) }))}
                            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="Zoom Out"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                            </svg>
                        </button>
                        <button
                            onClick={() => setTransform({ x: 0, y: 0, scale: 1 })}
                            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="Reset View"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Legend */}
                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-600">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Legend</h4>
                    <div className="space-y-2">
                        {[
                            { type: 'concept', label: 'Concept', color: '#3b82f6' },
                            { type: 'verse', label: 'Verse', color: '#10b981' },
                            { type: 'person', label: 'Person', color: '#f59e0b' },
                            { type: 'place', label: 'Place', color: '#8b5cf6' },
                            { type: 'theme', label: 'Theme', color: '#ef4444' },
                        ].map(item => (
                            <div key={item.type} className="flex items-center space-x-3">
                                <div
                                    className="w-4 h-4 rounded-full shadow-sm"
                                    style={{ backgroundColor: item.color }}
                                />
                                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {item.label}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Instructions */}
            <div className="absolute bottom-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-gray-200 dark:border-gray-600">
                <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    <div>🖱️ Drag to pan • 🎯 Drag nodes to move</div>
                    <div>🔍 Scroll to zoom • ⚡ Click reset to center</div>
                </div>
            </div>
        </div>
    );
}
