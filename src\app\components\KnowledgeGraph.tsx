"use client";

import { useEffect, useRef, useState } from "react";
import { GraphNode, GraphEdge } from "../types/graph";

interface KnowledgeGraphProps {
    nodes: GraphNode[];
    edges: GraphEdge[];
}

export default function KnowledgeGraph({ nodes, edges }: KnowledgeGraphProps) {
    const svgRef = useRef<SVGSVGElement>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    useEffect(() => {
        const updateDimensions = () => {
            if (svgRef.current) {
                const rect = svgRef.current.parentElement?.getBoundingClientRect();
                if (rect) {
                    setDimensions({ width: rect.width, height: rect.height - 20 });
                }
            }
        };

        updateDimensions();
        window.addEventListener('resize', updateDimensions);
        return () => window.removeEventListener('resize', updateDimensions);
    }, []);

    // Simple force-directed layout simulation
    const layoutNodes = (nodes: GraphNode[], edges: GraphEdge[], width: number, height: number) => {
        if (nodes.length === 0) return nodes;

        const layoutNodes = nodes.map((node) => ({
            ...node,
            position: node.position || {
                x: width / 2 + (Math.random() - 0.5) * 200,
                y: height / 2 + (Math.random() - 0.5) * 200,
            },
            vx: 0,
            vy: 0,
        }));

        // Simple simulation
        for (let i = 0; i < 100; i++) {
            // Apply forces
            layoutNodes.forEach((node, nodeIndex) => {
                let fx = 0, fy = 0;

                // Repulsion from other nodes
                layoutNodes.forEach((other, otherIndex) => {
                    if (nodeIndex !== otherIndex) {
                        const dx = node.position!.x - other.position!.x;
                        const dy = node.position!.y - other.position!.y;
                        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                        const force = 1000 / (distance * distance);
                        fx += (dx / distance) * force;
                        fy += (dy / distance) * force;
                    }
                });

                // Attraction from connected nodes
                edges.forEach(edge => {
                    const isSource = edge.source === node.id;
                    const isTarget = edge.target === node.id;

                    if (isSource || isTarget) {
                        const connectedId = isSource ? edge.target : edge.source;
                        const connectedNode = layoutNodes.find(n => n.id === connectedId);

                        if (connectedNode) {
                            const dx = connectedNode.position!.x - node.position!.x;
                            const dy = connectedNode.position!.y - node.position!.y;
                            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                            const force = distance * 0.01;
                            fx += (dx / distance) * force;
                            fy += (dy / distance) * force;
                        }
                    }
                });

                // Center force
                const centerX = width / 2;
                const centerY = height / 2;
                const centerDx = centerX - node.position!.x;
                const centerDy = centerY - node.position!.y;
                fx += centerDx * 0.001;
                fy += centerDy * 0.001;

                // Update velocity
                node.vx = (node.vx + fx) * 0.8;
                node.vy = (node.vy + fy) * 0.8;

                // Update position
                node.position!.x += node.vx;
                node.position!.y += node.vy;

                // Keep in bounds
                node.position!.x = Math.max(50, Math.min(width - 50, node.position!.x));
                node.position!.y = Math.max(50, Math.min(height - 50, node.position!.y));
            });
        }

        return layoutNodes;
    };

    const getNodeColor = (type: string) => {
        switch (type) {
            case 'concept': return '#3b82f6'; // blue
            case 'verse': return '#10b981'; // green
            case 'person': return '#f59e0b'; // amber
            case 'place': return '#8b5cf6'; // purple
            case 'theme': return '#ef4444'; // red
            default: return '#6b7280'; // gray
        }
    };

    const layoutedNodes = dimensions.width > 0 ? layoutNodes(nodes, edges, dimensions.width, dimensions.height) : [];

    if (nodes.length === 0) {
        return (
            <div className="h-full flex items-center justify-center text-slate-500 dark:text-slate-400">
                <div className="text-center">
                    <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <p className="text-sm">
                        Start a conversation to see the knowledge graph
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full relative">
            <svg
                ref={svgRef}
                width="100%"
                height="100%"
                className="border border-slate-200 dark:border-slate-600 rounded-lg bg-slate-50 dark:bg-slate-900"
            >
                {/* Edges */}
                {edges.map(edge => {
                    const sourceNode = layoutedNodes.find(n => n.id === edge.source);
                    const targetNode = layoutedNodes.find(n => n.id === edge.target);

                    if (!sourceNode || !targetNode) return null;

                    return (
                        <g key={edge.id}>
                            <line
                                x1={sourceNode.position!.x}
                                y1={sourceNode.position!.y}
                                x2={targetNode.position!.x}
                                y2={targetNode.position!.y}
                                stroke="#6b7280"
                                strokeWidth="2"
                                strokeOpacity="0.6"
                            />
                            {edge.label && (
                                <text
                                    x={(sourceNode.position!.x + targetNode.position!.x) / 2}
                                    y={(sourceNode.position!.y + targetNode.position!.y) / 2}
                                    textAnchor="middle"
                                    className="text-xs fill-slate-600 dark:fill-slate-400"
                                    dy="-5"
                                >
                                    {edge.label}
                                </text>
                            )}
                        </g>
                    );
                })}

                {/* Nodes */}
                {layoutedNodes.map(node => (
                    <g key={node.id}>
                        <circle
                            cx={node.position!.x}
                            cy={node.position!.y}
                            r={node.size || 20}
                            fill={node.color || getNodeColor(node.type)}
                            stroke="#fff"
                            strokeWidth="3"
                            className="cursor-pointer hover:opacity-80"
                        />
                        <text
                            x={node.position!.x}
                            y={node.position!.y + 5}
                            textAnchor="middle"
                            className="text-xs font-medium fill-white pointer-events-none"
                        >
                            {node.label.length > 8 ? node.label.substring(0, 8) + '...' : node.label}
                        </text>
                        {node.description && (
                            <title>{node.description}</title>
                        )}
                    </g>
                ))}
            </svg>

            {/* Legend */}
            <div className="absolute top-4 right-4 bg-white dark:bg-slate-800 rounded-lg p-3 shadow-lg border border-slate-200 dark:border-slate-600">
                <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">Legend</h4>
                <div className="space-y-1">
                    {[
                        { type: 'concept', label: 'Concept', color: '#3b82f6' },
                        { type: 'verse', label: 'Verse', color: '#10b981' },
                        { type: 'person', label: 'Person', color: '#f59e0b' },
                        { type: 'place', label: 'Place', color: '#8b5cf6' },
                        { type: 'theme', label: 'Theme', color: '#ef4444' },
                    ].map(item => (
                        <div key={item.type} className="flex items-center space-x-2">
                            <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: item.color }}
                            />
                            <span className="text-xs text-slate-600 dark:text-slate-400">
                                {item.label}
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}
