"use client";

import { useEffect, useRef, useState } from "react";
import { GraphNode, GraphEdge } from "../types/graph";

interface KnowledgeGraphProps {
    nodes: GraphNode[];
    edges: GraphEdge[];
}

export default function KnowledgeGraph({ nodes, edges }: KnowledgeGraphProps) {
    const svgRef = useRef<SVGSVGElement>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    useEffect(() => {
        const updateDimensions = () => {
            if (svgRef.current) {
                const rect = svgRef.current.parentElement?.getBoundingClientRect();
                if (rect) {
                    setDimensions({ width: rect.width, height: rect.height - 20 });
                }
            }
        };

        updateDimensions();
        window.addEventListener('resize', updateDimensions);
        return () => window.removeEventListener('resize', updateDimensions);
    }, []);

    // Improved force-directed layout simulation
    const layoutNodes = (nodes: GraphNode[], edges: GraphEdge[], width: number, height: number) => {
        if (nodes.length === 0) return nodes;

        const layoutNodes = nodes.map((node, index) => ({
            ...node,
            position: node.position || {
                // Better initial positioning in a circle
                x: width / 2 + Math.cos((index / nodes.length) * 2 * Math.PI) * Math.min(width, height) * 0.2,
                y: height / 2 + Math.sin((index / nodes.length) * 2 * Math.PI) * Math.min(width, height) * 0.2,
            },
            vx: 0,
            vy: 0,
        }));

        // Improved simulation with better spacing
        for (let i = 0; i < 150; i++) {
            // Apply forces
            layoutNodes.forEach((node, nodeIndex) => {
                let fx = 0, fy = 0;

                // Stronger repulsion from other nodes to reduce crowding
                layoutNodes.forEach((other, otherIndex) => {
                    if (nodeIndex !== otherIndex) {
                        const dx = node.position!.x - other.position!.x;
                        const dy = node.position!.y - other.position!.y;
                        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                        const minDistance = 80; // Minimum distance between nodes
                        const force = Math.max(2000 / (distance * distance), minDistance / distance);
                        fx += (dx / distance) * force;
                        fy += (dy / distance) * force;
                    }
                });

                // Weaker attraction from connected nodes
                edges.forEach(edge => {
                    const isSource = edge.source === node.id;
                    const isTarget = edge.target === node.id;

                    if (isSource || isTarget) {
                        const connectedId = isSource ? edge.target : edge.source;
                        const connectedNode = layoutNodes.find(n => n.id === connectedId);

                        if (connectedNode) {
                            const dx = connectedNode.position!.x - node.position!.x;
                            const dy = connectedNode.position!.y - node.position!.y;
                            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                            const idealDistance = 120; // Ideal distance between connected nodes
                            const force = (distance - idealDistance) * 0.02;
                            fx += (dx / distance) * force;
                            fy += (dy / distance) * force;
                        }
                    }
                });

                // Weaker center force
                const centerX = width / 2;
                const centerY = height / 2;
                const centerDx = centerX - node.position!.x;
                const centerDy = centerY - node.position!.y;
                fx += centerDx * 0.0005;
                fy += centerDy * 0.0005;

                // Update velocity with damping
                node.vx = (node.vx + fx) * 0.85;
                node.vy = (node.vy + fy) * 0.85;

                // Update position
                node.position!.x += node.vx;
                node.position!.y += node.vy;

                // Keep in bounds with more padding
                const padding = 80;
                node.position!.x = Math.max(padding, Math.min(width - padding, node.position!.x));
                node.position!.y = Math.max(padding, Math.min(height - padding, node.position!.y));
            });
        }

        return layoutNodes;
    };

    const getNodeColor = (type: string) => {
        switch (type) {
            case 'concept': return '#3b82f6'; // blue
            case 'verse': return '#10b981'; // green
            case 'person': return '#f59e0b'; // amber
            case 'place': return '#8b5cf6'; // purple
            case 'theme': return '#ef4444'; // red
            default: return '#6b7280'; // gray
        }
    };

    const getNodeSize = (type: string) => {
        switch (type) {
            case 'concept': return 25;
            case 'verse': return 22;
            case 'person': return 28;
            case 'place': return 24;
            case 'theme': return 30;
            default: return 20;
        }
    };

    const layoutedNodes = dimensions.width > 0 ? layoutNodes(nodes, edges, dimensions.width, dimensions.height) : [];

    if (nodes.length === 0) {
        return (
            <div className="h-full flex items-center justify-center text-slate-500 dark:text-slate-400">
                <div className="text-center">
                    <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <p className="text-sm">
                        Start a conversation to see the knowledge graph
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full relative">
            <svg
                ref={svgRef}
                width="100%"
                height="100%"
                className="rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"
            >
                {/* Edges */}
                {edges.map(edge => {
                    const sourceNode = layoutedNodes.find(n => n.id === edge.source);
                    const targetNode = layoutedNodes.find(n => n.id === edge.target);

                    if (!sourceNode || !targetNode) return null;

                    return (
                        <g key={edge.id}>
                            <line
                                x1={sourceNode.position!.x}
                                y1={sourceNode.position!.y}
                                x2={targetNode.position!.x}
                                y2={targetNode.position!.y}
                                stroke="#94a3b8"
                                strokeWidth="2"
                                strokeOpacity="0.4"
                                strokeDasharray="5,5"
                            />
                            {edge.label && (
                                <text
                                    x={(sourceNode.position!.x + targetNode.position!.x) / 2}
                                    y={(sourceNode.position!.y + targetNode.position!.y) / 2}
                                    textAnchor="middle"
                                    className="text-xs fill-gray-600 dark:fill-gray-400 font-medium"
                                    dy="-8"
                                    style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.8)' }}
                                >
                                    {edge.label}
                                </text>
                            )}
                        </g>
                    );
                })}

                {/* Nodes */}
                {layoutedNodes.map(node => {
                    const nodeSize = node.size || getNodeSize(node.type);
                    const nodeColor = node.color || getNodeColor(node.type);

                    return (
                        <g key={node.id}>
                            {/* Node shadow */}
                            <circle
                                cx={node.position!.x + 2}
                                cy={node.position!.y + 2}
                                r={nodeSize}
                                fill="rgba(0,0,0,0.1)"
                            />
                            {/* Main node */}
                            <circle
                                cx={node.position!.x}
                                cy={node.position!.y}
                                r={nodeSize}
                                fill={nodeColor}
                                stroke="#fff"
                                strokeWidth="3"
                                className="cursor-pointer hover:opacity-90 transition-opacity"
                                style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}
                            />
                            {/* Node label */}
                            <text
                                x={node.position!.x}
                                y={node.position!.y + 4}
                                textAnchor="middle"
                                className="text-xs font-semibold fill-white pointer-events-none"
                                style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                            >
                                {node.label.length > 10 ? node.label.substring(0, 10) + '...' : node.label}
                            </text>
                            {/* Tooltip */}
                            {node.description && (
                                <title>{`${node.label}: ${node.description}`}</title>
                            )}
                        </g>
                    );
                })}
            </svg>

            {/* Legend */}
            <div className="absolute top-4 right-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-600">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Legend</h4>
                <div className="space-y-2">
                    {[
                        { type: 'concept', label: 'Concept', color: '#3b82f6' },
                        { type: 'verse', label: 'Verse', color: '#10b981' },
                        { type: 'person', label: 'Person', color: '#f59e0b' },
                        { type: 'place', label: 'Place', color: '#8b5cf6' },
                        { type: 'theme', label: 'Theme', color: '#ef4444' },
                    ].map(item => (
                        <div key={item.type} className="flex items-center space-x-3">
                            <div
                                className="w-4 h-4 rounded-full shadow-sm"
                                style={{ backgroundColor: item.color }}
                            />
                            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                {item.label}
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}
