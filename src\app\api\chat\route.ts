import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GraphNode, GraphEdge } from '../../types/graph';

const genAI = new GoogleGenerativeAI(process.env.NEXT_GEMINI_API_KEY!);

async function generateBibleResponse(message: string): Promise<{
  response: string;
  nodes: GraphNode[];
  edges: GraphEdge[];
}> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    
    const prompt = `You are a biblical scholar AI assistant. Respond to the following question about the Bible: "${message}"

    Please provide:
        1. A comprehensive biblical response (keep it to 1-2 short paragraphs)
        2. Key biblical concepts, verses, people, places, or themes mentioned
        3. Relationships between these concepts

    IMPORTANT: Return ONLY valid JSON without any markdown formatting or code blocks. Use this exact structure:
    {
        "response": "Your biblical response here",
        "concepts": [
            {
            "id": "unique_id",
            "label": "Concept Name",
            "type": "concept",
            "description": "Brief description"
            }
        ],
        "relationships": [
            {
            "id": "edge_id",
            "source": "source_concept_id",
            "target": "target_concept_id",
            "label": "relationship_description",
            "type": "relates"
            }
        ]
    }`;

    const result = await model.generateContent(prompt);
    let responseText = result.response.text();
    
    // Clean up markdown formatting if present
    responseText = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
    
    // Parse the JSON response
    const parsedResponse = JSON.parse(responseText);
    
    return {
      response: parsedResponse.response,
      nodes: parsedResponse.concepts || [],
      edges: parsedResponse.relationships || []
    };
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    
    // Fallback response
    return {
      response: "I apologize, but I'm having trouble accessing biblical information right now. Please try again later.",
      nodes: [
        { id: "1", label: "Bible Study", type: "concept", description: "Exploring Scripture" },
        { id: "2", label: "Faith", type: "theme", description: "Trust in God" }
      ],
      edges: [
        { id: "e1", source: "1", target: "2", label: "builds", type: "relates" }
      ]
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Generate AI response with graph data
    const result = await generateBibleResponse(message);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
