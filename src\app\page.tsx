"use client";

import { useState } from "react";
import ChatInterface from "./components/ChatInterface";
import KnowledgeGraph from "./components/KnowledgeGraph";
import { GraphNode, GraphEdge } from "./types/graph";

export default function Home() {
    const [messages, setMessages] = useState<Array<{ role: string; content: string; nodes?: GraphNode[]; edges?: GraphEdge[] }>>([]);
    const [currentNodes, setCurrentNodes] = useState<GraphNode[]>([]);
    const [currentEdges, setCurrentEdges] = useState<GraphEdge[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const handleSendMessage = async (message: string) => {
        setIsLoading(true);

        // Add user message
        const userMessage = { role: "user", content: message };
        setMessages(prev => [...prev, userMessage]);

        try {
            // Call AI API
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message, history: messages }),
            });

            const data = await response.json();

            // Add AI response with graph data
            const aiMessage = {
                role: "assistant",
                content: data.response,
                nodes: data.nodes,
                edges: data.edges,
            };

            setMessages(prev => [...prev, aiMessage]);
            setCurrentNodes(data.nodes || []);
            setCurrentEdges(data.edges || []);
        } catch (error) {
            console.error('Error sending message:', error);
            setMessages(prev => [...prev, {
                role: "assistant",
                content: "I apologize, but I encountered an error. Please try again."
            }]);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
            {/* Header */}
            <header className="border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-sm">R</span>
                            </div>
                            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                                RootedGPT
                            </h1>
                            <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                                Bible Study AI
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-12rem)]">
                    {/* Chat Interface */}
                    <div className="flex flex-col">
                        <ChatInterface
                            messages={messages}
                            onSendMessage={handleSendMessage}
                            isLoading={isLoading}
                        />
                    </div>

                    {/* Knowledge Graph */}
                    <div className="flex flex-col">
                        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 h-full">
                            <div className="p-4 border-b border-slate-200 dark:border-slate-700">
                                <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                                    Knowledge Graph
                                </h2>
                                <p className="text-sm text-slate-600 dark:text-slate-400">
                                    Visual connections and insights
                                </p>
                            </div>
                            <div className="h-full p-4">
                                <KnowledgeGraph nodes={currentNodes} edges={currentEdges} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
