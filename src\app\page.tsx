"use client";

import { useState } from "react";
import ChatInterface from "./components/ChatInterface";
import KnowledgeGraph from "./components/KnowledgeGraph";
import { GraphNode, GraphEdge, Message } from "./types/graph";

export default function Home() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentNodes, setCurrentNodes] = useState<GraphNode[]>([]);
  const [currentEdges, setCurrentEdges] = useState<GraphEdge[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showKnowledgeGraph, setShowKnowledgeGraph] = useState(false);

  const handleSendMessage = async (message: string) => {
    setIsLoading(true);

    // Add user message
    const userMessage: Message = { role: "user", content: message };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Call AI API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message, history: messages }),
      });

      const data = await response.json();

      // Add AI response with graph data
      const aiMessage: Message = {
        role: "assistant",
        content: data.response,
        nodes: data.nodes,
        edges: data.edges,
      };

      setMessages(prev => [...prev, aiMessage]);
      setCurrentNodes(data.nodes || []);
      setCurrentEdges(data.edges || []);

      // Show knowledge graph if there are nodes
      if (data.nodes && data.nodes.length > 0) {
        setShowKnowledgeGraph(true);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        role: "assistant",
        content: "I apologize, but I encountered an error. Please try again."
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Header */}
      <header className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                RootedGPT
              </h1>
              <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                Bible Study AI
              </span>
            </div>

            {/* Knowledge Graph Toggle */}
            {currentNodes.length > 0 && (
              <button
                onClick={() => setShowKnowledgeGraph(!showKnowledgeGraph)}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>{showKnowledgeGraph ? 'Hide' : 'Show'} Knowledge Graph</span>
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto">
        {/* Chat Interface */}
        <div className="min-h-[calc(100vh-80px)]">
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
          />
        </div>

        {/* Knowledge Graph Modal/Overlay */}
        {showKnowledgeGraph && currentNodes.length > 0 && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-6xl h-[80vh] flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Knowledge Graph
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Visual connections and insights from your conversation
                  </p>
                </div>
                <button
                  onClick={() => setShowKnowledgeGraph(false)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="flex-1 p-4">
                <KnowledgeGraph nodes={currentNodes} edges={currentEdges} />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
