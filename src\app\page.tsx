"use client";

import { useState, useRef } from "react";
import ChatInterface from "./components/ChatInterface";
import KnowledgeGraph from "./components/KnowledgeGraph";
import { GraphNode, GraphEdge, Message } from "./types/graph";

export default function Home() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentNodes, setCurrentNodes] = useState<GraphNode[]>([]);
  const [currentEdges, setCurrentEdges] = useState<GraphEdge[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showKnowledgeGraph, setShowKnowledgeGraph] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState("");
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleSendMessage = async (message: string) => {
    setIsLoading(true);
    setStreamingMessage("");

    // Add user message
    const userMessage: Message = { role: "user", content: message };
    setMessages(prev => [...prev, userMessage]);

    // Add placeholder for AI response
    const aiMessage: Message = {
      role: "assistant",
      content: "",
      nodes: [],
      edges: [],
    };
    setMessages(prev => [...prev, aiMessage]);

    try {
      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Call AI API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message, history: messages }),
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      // Simulate streaming effect
      const fullResponse = data.response;
      let currentText = "";

      for (let i = 0; i < fullResponse.length; i++) {
        if (abortControllerRef.current?.signal.aborted) break;

        currentText += fullResponse[i];
        setStreamingMessage(currentText);

        // Update the last message with streaming content
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage.role === "assistant") {
            lastMessage.content = currentText;
          }
          return newMessages;
        });

        // Add delay for streaming effect
        await new Promise(resolve => setTimeout(resolve, 20));
      }

      // Final update with complete data
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage.role === "assistant") {
          lastMessage.content = data.response;
          lastMessage.nodes = data.nodes;
          lastMessage.edges = data.edges;
        }
        return newMessages;
      });

      setCurrentNodes(data.nodes || []);
      setCurrentEdges(data.edges || []);

      // Show knowledge graph if there are nodes
      if (data.nodes && data.nodes.length > 0) {
        setShowKnowledgeGraph(true);
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted');
        return;
      }

      console.error('Error sending message:', error);

      // Update the last message with error
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage.role === "assistant") {
          lastMessage.content = "I apologize, but I encountered an error. Please try again.";
        }
        return newMessages;
      });
    } finally {
      setIsLoading(false);
      setStreamingMessage("");
      abortControllerRef.current = null;
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex">
      {/* Main Content */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${showKnowledgeGraph ? 'mr-96' : ''}`}>
        {/* Header */}
        <header className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky top-0 z-10">
          <div className="px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">R</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  RootedGPT
                </h1>
                <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                  Bible Study AI
                </span>
              </div>

              {/* Knowledge Graph Toggle */}
              {currentNodes.length > 0 && (
                <button
                  onClick={() => setShowKnowledgeGraph(!showKnowledgeGraph)}
                  className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>{showKnowledgeGraph ? 'Hide' : 'Show'} Graph</span>
                </button>
              )}
            </div>
          </div>
        </header>

        {/* Chat Interface */}
        <main className="flex-1">
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
          />
        </main>
      </div>

      {/* Knowledge Graph Sidebar */}
      <div className={`fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 transform transition-transform duration-300 z-40 ${showKnowledgeGraph ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Knowledge Graph
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Visual connections and insights
              </p>
            </div>
            <button
              onClick={() => setShowKnowledgeGraph(false)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="flex-1 p-4">
            <KnowledgeGraph nodes={currentNodes} edges={currentEdges} />
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {showKnowledgeGraph && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setShowKnowledgeGraph(false)}
        />
      )}
    </div>
  );
}
